<template>
  <!--安全风险趋势 折线平滑图 -->
  <div class="box">
    <div class="box-header box-position-top">
      {{ title }}
      <!-- <span class="box-header-num">{{ total | numberToFormat }}</span> -->
    </div>

    <template v-if="list.length !== 0">
      <vsoc-chart
        :echartId="echartId"
        class="box-chart d-flex align-center"
        :option="option"
      ></vsoc-chart>
    </template>
    <template v-else>
      <div
        class="box-chart d-flex align-center color--primary justify-center fs-16"
      >
        None
      </div>
    </template>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import moment from 'moment'
import { lineOptionFn } from './chart'

export default {
  name: 'CardItem8',
  props: {
    title: {
      type: String,
      default: '',
    },
    echartId: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    xList: Array,
    total: {
      type: [Number, String],
      default: 0,
    },
  },
  components: {
    VsocChart,
  },
  data() {
    return {
      myChart: undefined,
    }
  },
  computed: {
    // yList() {
    //   // #f0da4c:1黄色（风险） #32FDB8：0绿色（活跃）
    //   let yData = []
    //   // let colorList = deepClone(cloudPieColor)
    //   this.list.forEach(item => {
    //     // let color = colorList[Math.floor(Math.random() * colorList.length)]
    //     let color =
    //       this.echartId === 'C004'
    //         ? ['#32fdb8', '#f0da4c']
    //         : ['#ff385d', '#f0da4c']
    //     yData.push({
    //       name: item.name,
    //       data: item.arrays.map(x => x.y),
    //       color: color[Number(item.dictId)],
    //     })
    //     // colorList.splice(
    //     //   colorList.findIndex(v => v === color),
    //     //   1,
    //     // )
    //   })
    //   return yData
    // },
    yList() {
      const yData = this.list.map(item => {
        // 根据 echartId 选择颜色方案
        const colorScheme = ['#ff385d', '#f0da4c']

        // 获取对应的颜色
        const itemColor = colorScheme[Number(item.dictId)] || colorScheme[0] // 默认使用第一个颜色

        return {
          name: item.name, // 假设需要国际化处理
          data: item.arrays.map(x => (x.y === 0 ? 0.5 : x.y)),
          color: itemColor,
        }
      })

      return yData
    },

    option() {
      if (!this.list || this.list.length === 0) {
        return lineOptionFn([], [])
      }

      // 提取并格式化 x 轴数据
      let xList = this.list[0].arrays.map(x => {
        const m = moment(x.x, ['YYYY-MM-DD', 'YYYY-MM-DD HH:mm:ss'], true)
        if (m.isValid()) {
          return m.format(x.x.includes(' ') ? 'HH:mm' : 'MM-DD')
        }
        return x.x // 如果解析失败，返回原始字符串
      })

      // let xList = this.list[0].arrays.map(x => format(new Date(x.x), 'MM/dd'))
      return lineOptionFn(xList, this.yList)
    },
  },
}
</script>
